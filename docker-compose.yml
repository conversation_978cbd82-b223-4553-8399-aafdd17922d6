version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: finpension-postgres
    environment:
      POSTGRES_DB: bookstore
      POSTGRES_USER: bookstore_user
      POSTGRES_PASSWORD: bookstore_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - bookstore-network

volumes:
  postgres_data:

networks:
  bookstore-network:
    driver: bridge
