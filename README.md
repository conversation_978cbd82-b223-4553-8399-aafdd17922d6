# Finpension Book Store API

A REST API for managing books built with Spring Boot using Hexagonal Architecture (Ports and Adapters pattern).

## Architecture

This project follows Hexagonal Architecture principles with clear separation of concerns:

### Domain Layer (Core Business Logic)
- `Book` - Domain entity
- `BookRepository` - Repository port (interface)
- `BookService` - Domain service with business logic

### Application Layer
- `BookUseCase` - Application service orchestrating domain services

### Infrastructure Layer (Adapters)
- `BookEntity` - JPA entity
- `BookJpaRepository` - Spring Data JPA repository
- `BookRepositoryAdapter` - Repository implementation
- `BookMapper` - Domain ↔ Entity mapping

### Presentation Layer
- `BookController` - REST API endpoints
- `BookRequestDto` / `BookResponseDto` - API DTOs
- `BookDtoMapper` - Domain ↔ DTO mapping

## Features

- **CRUD Operations**: Create, Read, Update, Delete books
- **Search**: Search books by author name or title
- **Validation**: Input validation with meaningful error messages
- **Database**: PostgreSQL with Flyway migrations
- **Testing**: Comprehensive unit and integration tests
- **Docker**: Docker Compose for easy database setup

## API Endpoints

### Books
- `GET /api/books` - Get all books
- `GET /api/books/{id}` - Get book by ID
- `POST /api/books` - Create a new book
- `PUT /api/books/{id}` - Update an existing book
- `DELETE /api/books/{id}` - Delete a book

### Search
- `GET /api/books/search/author?authorName={name}` - Search books by author
- `GET /api/books/search/title?title={title}` - Search books by title

## Book Entity

```json
{
  "id": 1,
  "title": "The Great Gatsby",
  "authorName": "F. Scott Fitzgerald",
  "numberOfPages": 180,
  "publishedDate": "1925-04-10"
}
```

## Getting Started

### Prerequisites
- Java 21 or higher
- Maven 3.6+
- Docker and Docker Compose

### Setup Database

1. Start PostgreSQL using Docker Compose:
```bash
docker-compose up -d
```

This will start PostgreSQL on port 5432 with:
- Database: `bookstore`
- Username: `bookstore_user`
- Password: `bookstore_password`

### Run the Application

1. Build the project:
```bash
./mvnw clean compile
```

2. Run the application:
```bash
./mvnw spring-boot:run
```

The application will start on `http://localhost:8080`

### Database Migration

The application uses Flyway for database migrations. On startup, it will:
1. Create the `books` table
2. Insert seed data (15 books)

### Testing

Run all tests:
```bash
./mvnw test
```

Run specific test classes:
```bash
./mvnw test -Dtest=BookServiceTest
./mvnw test -Dtest=BookControllerIntegrationTest
```

## Sample API Calls

### Create a Book
```bash
curl -X POST http://localhost:8080/api/books \
  -H "Content-Type: application/json" \
  -d '{
    "title": "New Book",
    "authorName": "New Author",
    "numberOfPages": 250,
    "publishedDate": "2023-01-01"
  }'
```

### Get All Books
```bash
curl http://localhost:8080/api/books
```

### Get Book by ID
```bash
curl http://localhost:8080/api/books/1
```

### Update a Book
```bash
curl -X PUT http://localhost:8080/api/books/1 \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Updated Title",
    "authorName": "Updated Author",
    "numberOfPages": 300,
    "publishedDate": "2023-02-01"
  }'
```

### Delete a Book
```bash
curl -X DELETE http://localhost:8080/api/books/1
```

### Search Books by Author
```bash
curl "http://localhost:8080/api/books/search/author?authorName=Tolkien"
```

## Project Structure

```
src/
├── main/
│   ├── java/com/example/finpension/
│   │   ├── application/usecase/          # Application layer
│   │   ├── configuration/                # Spring configuration
│   │   ├── domain/                       # Domain layer
│   │   │   ├── model/                    # Domain entities
│   │   │   ├── port/                     # Repository interfaces
│   │   │   └── service/                  # Domain services
│   │   ├── infrastructure/               # Infrastructure layer
│   │   │   └── persistence/              # Database adapters
│   │   └── presentation/                 # Presentation layer
│   │       ├── controller/               # REST controllers
│   │       ├── dto/                      # API DTOs
│   │       ├── exception/                # Exception handlers
│   │       └── mapper/                   # DTO mappers
│   └── resources/
│       ├── db/migration/                 # Flyway migrations
│       └── application.yml               # Application configuration
└── test/                                 # Test classes
```

## Technologies Used

- **Spring Boot 4.0.0-SNAPSHOT** - Application framework
- **Spring Data JPA** - Data access layer
- **PostgreSQL** - Production database
- **H2** - Test database
- **Flyway** - Database migrations
- **JUnit 5** - Testing framework
- **Mockito** - Mocking framework
- **AssertJ** - Fluent assertions
- **Docker Compose** - Container orchestration

## Seed Data

The application comes with 15 pre-loaded books including classics like:
- The Great Gatsby
- To Kill a Mockingbird
- 1984
- Pride and Prejudice
- The Lord of the Rings
- And more...
