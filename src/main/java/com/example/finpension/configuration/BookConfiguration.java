package com.example.finpension.configuration;

import com.example.finpension.domain.port.BookRepository;
import com.example.finpension.domain.service.BookService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for Book-related beans.
 * This wires together the hexagonal architecture components.
 */
@Configuration
public class BookConfiguration {
    
    /**
     * Create BookService bean
     * @param bookRepository the book repository implementation
     * @return the book service
     */
    @Bean
    public BookService bookService(BookRepository bookRepository) {
        return new BookService(bookRepository);
    }
}
