package com.example.finpension.application.usecase;

import com.example.finpension.domain.model.Book;
import com.example.finpension.domain.service.BookService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * Application use case for Book operations.
 * This orchestrates domain services and acts as the application layer
 * in hexagonal architecture.
 */
@Service
public class BookUseCase {
    
    private final BookService bookService;
    
    public BookUseCase(BookService bookService) {
        this.bookService = bookService;
    }
    
    /**
     * Create a new book
     * @param book the book to create
     * @return the created book
     */
    public Book createBook(Book book) {
        return bookService.createBook(book);
    }
    
    /**
     * Get a book by ID
     * @param id the book ID
     * @return Optional containing the book if found
     */
    public Optional<Book> getBookById(Long id) {
        return bookService.getBookById(id);
    }
    
    /**
     * Get all books
     * @return list of all books
     */
    public List<Book> getAllBooks() {
        return bookService.getAllBooks();
    }
    
    /**
     * Update an existing book
     * @param id the book ID to update
     * @param updatedBook the updated book data
     * @return the updated book
     */
    public Book updateBook(Long id, Book updatedBook) {
        return bookService.updateBook(id, updatedBook);
    }
    
    /**
     * Delete a book by ID
     * @param id the book ID to delete
     * @return true if the book was deleted, false if it didn't exist
     */
    public boolean deleteBook(Long id) {
        return bookService.deleteBook(id);
    }
    
    /**
     * Search books by author name
     * @param authorName the author name to search for
     * @return list of books by the specified author
     */
    public List<Book> getBooksByAuthor(String authorName) {
        return bookService.getBooksByAuthor(authorName);
    }
    
    /**
     * Search books by title
     * @param title the title to search for
     * @return list of books matching the title
     */
    public List<Book> getBooksByTitle(String title) {
        return bookService.getBooksByTitle(title);
    }
}
