package com.example.finpension.presentation.controller;

import com.example.finpension.application.usecase.BookUseCase;
import com.example.finpension.domain.model.Book;
import com.example.finpension.presentation.dto.BookRequestDto;
import com.example.finpension.presentation.dto.BookResponseDto;
import com.example.finpension.presentation.mapper.BookDtoMapper;
import jakarta.validation.Valid;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * REST Controller for Book operations.
 * This is part of the presentation layer in hexagonal architecture.
 */
@RestController
@RequestMapping("/api/books")
@CrossOrigin(origins = "*")
public class BookController {
    
    private final BookUseCase bookUseCase;
    private final BookDtoMapper bookDtoMapper;
    
    public BookController(BookUseCase bookUseCase, BookDtoMapper bookDtoMapper) {
        this.bookUseCase = bookUseCase;
        this.bookDtoMapper = bookDtoMapper;
    }
    
    /**
     * Get all books
     * @return list of all books
     */
    @GetMapping
    public ResponseEntity<List<BookResponseDto>> getAllBooks() {
        List<Book> books = bookUseCase.getAllBooks();
        List<BookResponseDto> responseDtos = bookDtoMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }
    
    /**
     * Get a book by ID
     * @param id the book ID
     * @return the book if found, 404 if not found
     */
    @GetMapping("/{id}")
    public ResponseEntity<BookResponseDto> getBookById(@PathVariable Long id) {
        Optional<Book> book = bookUseCase.getBookById(id);
        
        if (book.isPresent()) {
            BookResponseDto responseDto = bookDtoMapper.toResponseDto(book.get());
            return ResponseEntity.ok(responseDto);
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * Create a new book
     * @param requestDto the book data
     * @return the created book
     */
    @PostMapping
    public ResponseEntity<BookResponseDto> createBook(@Valid @RequestBody BookRequestDto requestDto) {
        Book book = bookDtoMapper.toDomain(requestDto);
        Book createdBook = bookUseCase.createBook(book);
        BookResponseDto responseDto = bookDtoMapper.toResponseDto(createdBook);
        
        return ResponseEntity.status(HttpStatus.CREATED).body(responseDto);
    }
    
    /**
     * Update an existing book
     * @param id the book ID to update
     * @param requestDto the updated book data
     * @return the updated book if successful, 404 if book not found
     */
    @PutMapping("/{id}")
    public ResponseEntity<BookResponseDto> updateBook(
            @PathVariable Long id, 
            @Valid @RequestBody BookRequestDto requestDto) {
        
        try {
            Book updatedBookData = bookDtoMapper.toDomain(requestDto);
            Book updatedBook = bookUseCase.updateBook(id, updatedBookData);
            BookResponseDto responseDto = bookDtoMapper.toResponseDto(updatedBook);
            
            return ResponseEntity.ok(responseDto);
        } catch (IllegalArgumentException e) {
            if (e.getMessage().contains("does not exist")) {
                return ResponseEntity.notFound().build();
            }
            return ResponseEntity.badRequest().build();
        }
    }
    
    /**
     * Delete a book by ID
     * @param id the book ID to delete
     * @return 204 if deleted successfully, 404 if book not found
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteBook(@PathVariable Long id) {
        boolean deleted = bookUseCase.deleteBook(id);
        
        if (deleted) {
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
    
    /**
     * Search books by author name
     * @param authorName the author name to search for
     * @return list of books by the specified author
     */
    @GetMapping("/search/author")
    public ResponseEntity<List<BookResponseDto>> getBooksByAuthor(@RequestParam String authorName) {
        List<Book> books = bookUseCase.getBooksByAuthor(authorName);
        List<BookResponseDto> responseDtos = bookDtoMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }
    
    /**
     * Search books by title
     * @param title the title to search for
     * @return list of books matching the title
     */
    @GetMapping("/search/title")
    public ResponseEntity<List<BookResponseDto>> getBooksByTitle(@RequestParam String title) {
        List<Book> books = bookUseCase.getBooksByTitle(title);
        List<BookResponseDto> responseDtos = bookDtoMapper.toResponseDtoList(books);
        return ResponseEntity.ok(responseDtos);
    }
}
