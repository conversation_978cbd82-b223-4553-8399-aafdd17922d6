package com.example.finpension.presentation.mapper;

import com.example.finpension.domain.model.Book;
import com.example.finpension.presentation.dto.BookRequestDto;
import com.example.finpension.presentation.dto.BookResponseDto;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Mapper between Book domain model and DTOs.
 * This is part of the presentation layer in hexagonal architecture.
 */
@Component
public class BookDtoMapper {
    
    /**
     * Convert BookRequestDto to domain Book
     * @param requestDto the request DTO
     * @return the domain book
     */
    public Book toDomain(BookRequestDto requestDto) {
        if (requestDto == null) {
            return null;
        }
        
        return new Book(
                requestDto.getTitle(),
                requestDto.getAuthorName(),
                requestDto.getNumberOfPages(),
                requestDto.getPublishedDate()
        );
    }
    
    /**
     * Convert domain Book to BookResponseDto
     * @param book the domain book
     * @return the response DTO
     */
    public BookResponseDto toResponseDto(Book book) {
        if (book == null) {
            return null;
        }
        
        return new BookResponseDto(
                book.getId(),
                book.getTitle(),
                book.getAuthorName(),
                book.getNumberOfPages(),
                book.getPublishedDate()
        );
    }
    
    /**
     * Convert list of domain Books to list of BookResponseDtos
     * @param books the list of domain books
     * @return the list of response DTOs
     */
    public List<BookResponseDto> toResponseDtoList(List<Book> books) {
        if (books == null) {
            return null;
        }
        
        return books.stream()
                .map(this::toResponseDto)
                .collect(Collectors.toList());
    }
    
    /**
     * Update domain Book with data from BookRequestDto
     * @param book the domain book to update
     * @param requestDto the request DTO with new data
     * @return the updated domain book
     */
    public Book updateFromDto(Book book, BookRequestDto requestDto) {
        if (book == null || requestDto == null) {
            return book;
        }
        
        book.setTitle(requestDto.getTitle());
        book.setAuthorName(requestDto.getAuthorName());
        book.setNumberOfPages(requestDto.getNumberOfPages());
        book.setPublishedDate(requestDto.getPublishedDate());
        
        return book;
    }
}
