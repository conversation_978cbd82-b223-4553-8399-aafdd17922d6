package com.example.finpension.presentation.dto;

import jakarta.validation.constraints.*;
import java.time.LocalDate;

/**
 * DTO for Book requests (POST and PUT operations).
 * This is part of the presentation layer in hexagonal architecture.
 */
public class BookRequestDto {
    
    @NotBlank(message = "Title is required")
    @Size(max = 255, message = "Title must not exceed 255 characters")
    private String title;
    
    @NotBlank(message = "Author name is required")
    @Size(max = 255, message = "Author name must not exceed 255 characters")
    private String authorName;
    
    @NotNull(message = "Number of pages is required")
    @Positive(message = "Number of pages must be positive")
    private Integer numberOfPages;
    
    @NotNull(message = "Published date is required")
    @PastOrPresent(message = "Published date cannot be in the future")
    private LocalDate publishedDate;
    
    // Default constructor
    public BookRequestDto() {}
    
    // Full constructor
    public BookRequestDto(String title, String authorName, Integer numberOfPages, LocalDate publishedDate) {
        this.title = title;
        this.authorName = authorName;
        this.numberOfPages = numberOfPages;
        this.publishedDate = publishedDate;
    }
    
    // Getters and Setters
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public Integer getNumberOfPages() {
        return numberOfPages;
    }
    
    public void setNumberOfPages(Integer numberOfPages) {
        this.numberOfPages = numberOfPages;
    }
    
    public LocalDate getPublishedDate() {
        return publishedDate;
    }
    
    public void setPublishedDate(LocalDate publishedDate) {
        this.publishedDate = publishedDate;
    }
    
    @Override
    public String toString() {
        return "BookRequestDto{" +
                "title='" + title + '\'' +
                ", authorName='" + authorName + '\'' +
                ", numberOfPages=" + numberOfPages +
                ", publishedDate=" + publishedDate +
                '}';
    }
}
