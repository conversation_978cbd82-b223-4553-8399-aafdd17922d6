package com.example.finpension.presentation.dto;

import java.time.LocalDate;

/**
 * DTO for Book responses (GET operations).
 * This is part of the presentation layer in hexagonal architecture.
 */
public class BookResponseDto {
    
    private Long id;
    private String title;
    private String authorName;
    private Integer numberOfPages;
    private LocalDate publishedDate;
    
    // Default constructor
    public BookResponseDto() {}
    
    // Full constructor
    public BookResponseDto(Long id, String title, String authorName, Integer numberOfPages, LocalDate publishedDate) {
        this.id = id;
        this.title = title;
        this.authorName = authorName;
        this.numberOfPages = numberOfPages;
        this.publishedDate = publishedDate;
    }
    
    // Getters and Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
    
    public String getAuthorName() {
        return authorName;
    }
    
    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }
    
    public Integer getNumberOfPages() {
        return numberOfPages;
    }
    
    public void setNumberOfPages(Integer numberOfPages) {
        this.numberOfPages = numberOfPages;
    }
    
    public LocalDate getPublishedDate() {
        return publishedDate;
    }
    
    public void setPublishedDate(LocalDate publishedDate) {
        this.publishedDate = publishedDate;
    }
    
    @Override
    public String toString() {
        return "BookResponseDto{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", authorName='" + authorName + '\'' +
                ", numberOfPages=" + numberOfPages +
                ", publishedDate=" + publishedDate +
                '}';
    }
}
