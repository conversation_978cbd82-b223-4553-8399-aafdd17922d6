package com.example.finpension.infrastructure.persistence.adapter;

import com.example.finpension.domain.model.Book;
import com.example.finpension.domain.port.BookRepository;
import com.example.finpension.infrastructure.persistence.entity.BookEntity;
import com.example.finpension.infrastructure.persistence.mapper.BookMapper;
import com.example.finpension.infrastructure.persistence.repository.BookJpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Repository adapter that implements the domain BookRepository port.
 * This is part of the infrastructure layer in hexagonal architecture.
 */
@Repository
public class BookRepositoryAdapter implements BookRepository {
    
    private final BookJpaRepository bookJpaRepository;
    private final BookMapper bookMapper;
    
    public BookRepositoryAdapter(BookJpaRepository bookJpaRepository, BookMapper bookMapper) {
        this.bookJpaRepository = bookJpaRepository;
        this.bookMapper = bookMapper;
    }
    
    @Override
    public Book save(Book book) {
        BookEntity bookEntity = bookMapper.toEntity(book);
        BookEntity savedEntity = bookJpaRepository.save(bookEntity);
        return bookMapper.toDomain(savedEntity);
    }
    
    @Override
    public Optional<Book> findById(Long id) {
        return bookJpaRepository.findById(id)
                .map(bookMapper::toDomain);
    }
    
    @Override
    public List<Book> findAll() {
        return bookJpaRepository.findAll()
                .stream()
                .map(bookMapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Book> findByAuthorName(String authorName) {
        return bookJpaRepository.findByAuthorName(authorName)
                .stream()
                .map(bookMapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<Book> findByTitleContainingIgnoreCase(String title) {
        return bookJpaRepository.findByTitleContainingIgnoreCase(title)
                .stream()
                .map(bookMapper::toDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public boolean deleteById(Long id) {
        if (bookJpaRepository.existsById(id)) {
            bookJpaRepository.deleteById(id);
            return true;
        }
        return false;
    }
    
    @Override
    public boolean existsById(Long id) {
        return bookJpaRepository.existsById(id);
    }
}
