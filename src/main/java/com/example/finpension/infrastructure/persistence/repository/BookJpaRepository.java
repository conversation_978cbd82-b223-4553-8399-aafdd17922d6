package com.example.finpension.infrastructure.persistence.repository;

import com.example.finpension.infrastructure.persistence.entity.BookEntity;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * JPA Repository interface for BookEntity.
 * This is part of the infrastructure layer in hexagonal architecture.
 */
@Repository
public interface BookJpaRepository extends JpaRepository<BookEntity, Long> {
    
    /**
     * Find books by author name
     * @param authorName the author name to search for
     * @return list of books by the specified author
     */
    List<BookEntity> findByAuthorName(String authorName);
    
    /**
     * Find books by title containing the given string (case-insensitive)
     * @param title the title to search for
     * @return list of books matching the title
     */
    List<BookEntity> findByTitleContainingIgnoreCase(String title);
    
    /**
     * Find books by author name containing the given string (case-insensitive)
     * @param authorName the author name to search for
     * @return list of books by authors matching the name
     */
    List<BookEntity> findByAuthorNameContainingIgnoreCase(String authorName);
    
    /**
     * Custom query to find books by title or author name
     * @param searchTerm the search term
     * @return list of books matching the search term
     */
    @Query("SELECT b FROM BookEntity b WHERE " +
           "LOWER(b.title) LIKE LOWER(CONCAT('%', :searchTerm, '%')) OR " +
           "LOWER(b.authorName) LIKE LOWER(CONCAT('%', :searchTerm, '%'))")
    List<BookEntity> findByTitleOrAuthorNameContainingIgnoreCase(@Param("searchTerm") String searchTerm);
}
