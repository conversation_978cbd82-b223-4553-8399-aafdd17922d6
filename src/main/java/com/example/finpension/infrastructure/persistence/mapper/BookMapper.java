package com.example.finpension.infrastructure.persistence.mapper;

import com.example.finpension.domain.model.Book;
import com.example.finpension.infrastructure.persistence.entity.BookEntity;
import org.springframework.stereotype.Component;

/**
 * Mapper between Book domain model and BookEntity.
 * This is part of the infrastructure layer in hexagonal architecture.
 */
@Component
public class BookMapper {
    
    /**
     * Convert domain Book to BookEntity
     * @param book the domain book
     * @return the book entity
     */
    public BookEntity toEntity(Book book) {
        if (book == null) {
            return null;
        }
        
        BookEntity entity = new BookEntity();
        entity.setId(book.getId());
        entity.setTitle(book.getTitle());
        entity.setAuthorName(book.getAuthorName());
        entity.setNumberOfPages(book.getNumberOfPages());
        entity.setPublishedDate(book.getPublishedDate());
        
        return entity;
    }
    
    /**
     * Convert BookEntity to domain Book
     * @param entity the book entity
     * @return the domain book
     */
    public Book toDomain(BookEntity entity) {
        if (entity == null) {
            return null;
        }
        
        return new Book(
                entity.getId(),
                entity.getTitle(),
                entity.getAuthorName(),
                entity.getNumberOfPages(),
                entity.getPublishedDate()
        );
    }
}
