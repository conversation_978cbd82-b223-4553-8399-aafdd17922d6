package com.example.finpension.domain.port;

import com.example.finpension.domain.model.Book;

import java.util.List;
import java.util.Optional;

/**
 * Repository port (interface) for Book entity.
 * This is part of the domain layer in hexagonal architecture.
 * The actual implementation will be provided by the infrastructure layer.
 */
public interface BookRepository {
    
    /**
     * Save a book (create or update)
     * @param book the book to save
     * @return the saved book with generated ID if it was a new book
     */
    Book save(Book book);
    
    /**
     * Find a book by its ID
     * @param id the book ID
     * @return Optional containing the book if found, empty otherwise
     */
    Optional<Book> findById(Long id);
    
    /**
     * Find all books
     * @return list of all books
     */
    List<Book> findAll();
    
    /**
     * Find books by author name
     * @param authorName the author name to search for
     * @return list of books by the specified author
     */
    List<Book> findByAuthorName(String authorName);
    
    /**
     * Find books by title (case-insensitive partial match)
     * @param title the title to search for
     * @return list of books matching the title
     */
    List<Book> findByTitleContainingIgnoreCase(String title);
    
    /**
     * Delete a book by its ID
     * @param id the book ID to delete
     * @return true if the book was deleted, false if it didn't exist
     */
    boolean deleteById(Long id);
    
    /**
     * Check if a book exists by ID
     * @param id the book ID
     * @return true if the book exists, false otherwise
     */
    boolean existsById(Long id);
}
