package com.example.finpension.domain.model;

import java.time.LocalDate;
import java.util.Objects;

/**
 * Book domain entity representing the core business object.
 * This is part of the domain layer in hexagonal architecture.
 */
public class Book {
    private Long id;
    private String title;
    private String authorName;
    private Integer numberOfPages;
    private LocalDate publishedDate;

    // Default constructor
    public Book() {}

    // Constructor without ID (for creating new books)
    public Book(String title, String authorName, Integer numberOfPages, LocalDate publishedDate) {
        this.title = title;
        this.authorName = authorName;
        this.numberOfPages = numberOfPages;
        this.publishedDate = publishedDate;
    }

    // Full constructor
    public Book(Long id, String title, String authorName, Integer numberOfPages, LocalDate publishedDate) {
        this.id = id;
        this.title = title;
        this.authorName = authorName;
        this.numberOfPages = numberOfPages;
        this.publishedDate = publishedDate;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getAuthorName() {
        return authorName;
    }

    public void setAuthorName(String authorName) {
        this.authorName = authorName;
    }

    public Integer getNumberOfPages() {
        return numberOfPages;
    }

    public void setNumberOfPages(Integer numberOfPages) {
        this.numberOfPages = numberOfPages;
    }

    public LocalDate getPublishedDate() {
        return publishedDate;
    }

    public void setPublishedDate(LocalDate publishedDate) {
        this.publishedDate = publishedDate;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Book book = (Book) o;
        return Objects.equals(id, book.id) &&
               Objects.equals(title, book.title) &&
               Objects.equals(authorName, book.authorName) &&
               Objects.equals(numberOfPages, book.numberOfPages) &&
               Objects.equals(publishedDate, book.publishedDate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, title, authorName, numberOfPages, publishedDate);
    }

    @Override
    public String toString() {
        return "Book{" +
                "id=" + id +
                ", title='" + title + '\'' +
                ", authorName='" + authorName + '\'' +
                ", numberOfPages=" + numberOfPages +
                ", publishedDate=" + publishedDate +
                '}';
    }
}
