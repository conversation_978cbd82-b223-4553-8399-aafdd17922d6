package com.example.finpension.domain.service;

import com.example.finpension.domain.model.Book;
import com.example.finpension.domain.port.BookRepository;

import java.util.List;
import java.util.Optional;

/**
 * Domain service for Book business logic.
 * This is part of the domain layer in hexagonal architecture.
 */
public class BookService {
    
    private final BookRepository bookRepository;
    
    public BookService(BookRepository bookRepository) {
        this.bookRepository = bookRepository;
    }
    
    /**
     * Create a new book
     * @param book the book to create
     * @return the created book with generated ID
     * @throws IllegalArgumentException if book data is invalid
     */
    public Book createBook(Book book) {
        validateBook(book);
        return bookRepository.save(book);
    }
    
    /**
     * Get a book by ID
     * @param id the book ID
     * @return Optional containing the book if found
     */
    public Optional<Book> getBookById(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("Book ID must be a positive number");
        }
        return bookRepository.findById(id);
    }
    
    /**
     * Get all books
     * @return list of all books
     */
    public List<Book> getAllBooks() {
        return bookRepository.findAll();
    }
    
    /**
     * Update an existing book
     * @param id the book ID to update
     * @param updatedBook the updated book data
     * @return the updated book
     * @throws IllegalArgumentException if book doesn't exist or data is invalid
     */
    public Book updateBook(Long id, Book updatedBook) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("Book ID must be a positive number");
        }
        
        if (!bookRepository.existsById(id)) {
            throw new IllegalArgumentException("Book with ID " + id + " does not exist");
        }
        
        validateBook(updatedBook);
        updatedBook.setId(id);
        return bookRepository.save(updatedBook);
    }
    
    /**
     * Delete a book by ID
     * @param id the book ID to delete
     * @return true if the book was deleted, false if it didn't exist
     */
    public boolean deleteBook(Long id) {
        if (id == null || id <= 0) {
            throw new IllegalArgumentException("Book ID must be a positive number");
        }
        return bookRepository.deleteById(id);
    }
    
    /**
     * Search books by author name
     * @param authorName the author name to search for
     * @return list of books by the specified author
     */
    public List<Book> getBooksByAuthor(String authorName) {
        if (authorName == null || authorName.trim().isEmpty()) {
            throw new IllegalArgumentException("Author name cannot be null or empty");
        }
        return bookRepository.findByAuthorName(authorName.trim());
    }
    
    /**
     * Search books by title
     * @param title the title to search for
     * @return list of books matching the title
     */
    public List<Book> getBooksByTitle(String title) {
        if (title == null || title.trim().isEmpty()) {
            throw new IllegalArgumentException("Title cannot be null or empty");
        }
        return bookRepository.findByTitleContainingIgnoreCase(title.trim());
    }
    
    /**
     * Validate book data
     * @param book the book to validate
     * @throws IllegalArgumentException if book data is invalid
     */
    private void validateBook(Book book) {
        if (book == null) {
            throw new IllegalArgumentException("Book cannot be null");
        }
        
        if (book.getTitle() == null || book.getTitle().trim().isEmpty()) {
            throw new IllegalArgumentException("Book title cannot be null or empty");
        }
        
        if (book.getAuthorName() == null || book.getAuthorName().trim().isEmpty()) {
            throw new IllegalArgumentException("Author name cannot be null or empty");
        }
        
        if (book.getNumberOfPages() == null || book.getNumberOfPages() <= 0) {
            throw new IllegalArgumentException("Number of pages must be a positive number");
        }
        
        if (book.getPublishedDate() == null) {
            throw new IllegalArgumentException("Published date cannot be null");
        }
    }
}
