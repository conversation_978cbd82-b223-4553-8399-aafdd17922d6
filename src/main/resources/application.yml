spring:
  application:
    name: finpension-bookstore
  
  datasource:
    url: ******************************************
    username: bookstore_user
    password: bookstore_password
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true

server:
  port: 8080

logging:
  level:
    com.example.finpension: DEBUG
    org.springframework.web: DEBUG
