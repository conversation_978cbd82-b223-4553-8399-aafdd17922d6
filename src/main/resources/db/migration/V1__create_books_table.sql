CREATE TABLE books (
    id BIGSERIAL PRIMARY KEY,
    title VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    author_name VARCHAR(255) NOT NULL,
    number_of_pages INTEGER NOT NULL CHECK (number_of_pages > 0),
    published_date DATE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_books_author_name ON books(author_name);
CREATE INDEX idx_books_title ON books(title);
CREATE INDEX idx_books_published_date ON books(published_date);
