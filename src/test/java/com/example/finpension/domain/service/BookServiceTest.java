package com.example.finpension.domain.service;

import com.example.finpension.domain.model.Book;
import com.example.finpension.domain.port.BookRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookServiceTest {
    
    @Mock
    private BookRepository bookRepository;
    
    private BookService bookService;
    
    private Book validBook;
    
    @BeforeEach
    void setUp() {
        bookService = new BookService(bookRepository);
        validBook = new Book("Test Title", "Test Author", 200, LocalDate.of(2023, 1, 1));
    }
    
    @Test
    void createBook_WithValidBook_ShouldReturnSavedBook() {
        // Given
        Book savedBook = new Book(1L, "Test Title", "Test Author", 200, LocalDate.of(2023, 1, 1));
        when(bookRepository.save(any(Book.class))).thenReturn(savedBook);
        
        // When
        Book result = bookService.createBook(validBook);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getTitle()).isEqualTo("Test Title");
        verify(bookRepository).save(validBook);
    }
    
    @Test
    void createBook_WithNullBook_ShouldThrowException() {
        // When & Then
        assertThatThrownBy(() -> bookService.createBook(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Book cannot be null");
    }
    
    @Test
    void createBook_WithEmptyTitle_ShouldThrowException() {
        // Given
        Book bookWithEmptyTitle = new Book("", "Test Author", 200, LocalDate.of(2023, 1, 1));
        
        // When & Then
        assertThatThrownBy(() -> bookService.createBook(bookWithEmptyTitle))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Book title cannot be null or empty");
    }
    
    @Test
    void createBook_WithNegativePages_ShouldThrowException() {
        // Given
        Book bookWithNegativePages = new Book("Test Title", "Test Author", -10, LocalDate.of(2023, 1, 1));
        
        // When & Then
        assertThatThrownBy(() -> bookService.createBook(bookWithNegativePages))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Number of pages must be a positive number");
    }
    
    @Test
    void getBookById_WithValidId_ShouldReturnBook() {
        // Given
        Long bookId = 1L;
        Book expectedBook = new Book(bookId, "Test Title", "Test Author", 200, LocalDate.of(2023, 1, 1));
        when(bookRepository.findById(bookId)).thenReturn(Optional.of(expectedBook));
        
        // When
        Optional<Book> result = bookService.getBookById(bookId);
        
        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(expectedBook);
        verify(bookRepository).findById(bookId);
    }
    
    @Test
    void getBookById_WithInvalidId_ShouldThrowException() {
        // When & Then
        assertThatThrownBy(() -> bookService.getBookById(-1L))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Book ID must be a positive number");
    }
    
    @Test
    void getAllBooks_ShouldReturnAllBooks() {
        // Given
        List<Book> expectedBooks = Arrays.asList(
                new Book(1L, "Book 1", "Author 1", 100, LocalDate.of(2023, 1, 1)),
                new Book(2L, "Book 2", "Author 2", 200, LocalDate.of(2023, 2, 1))
        );
        when(bookRepository.findAll()).thenReturn(expectedBooks);
        
        // When
        List<Book> result = bookService.getAllBooks();
        
        // Then
        assertThat(result).hasSize(2);
        assertThat(result).isEqualTo(expectedBooks);
        verify(bookRepository).findAll();
    }
    
    @Test
    void updateBook_WithValidData_ShouldReturnUpdatedBook() {
        // Given
        Long bookId = 1L;
        Book updatedBookData = new Book("Updated Title", "Updated Author", 300, LocalDate.of(2023, 3, 1));
        Book savedBook = new Book(bookId, "Updated Title", "Updated Author", 300, LocalDate.of(2023, 3, 1));
        
        when(bookRepository.existsById(bookId)).thenReturn(true);
        when(bookRepository.save(any(Book.class))).thenReturn(savedBook);
        
        // When
        Book result = bookService.updateBook(bookId, updatedBookData);
        
        // Then
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(bookId);
        assertThat(result.getTitle()).isEqualTo("Updated Title");
        verify(bookRepository).existsById(bookId);
        verify(bookRepository).save(any(Book.class));
    }
    
    @Test
    void updateBook_WithNonExistentId_ShouldThrowException() {
        // Given
        Long bookId = 999L;
        when(bookRepository.existsById(bookId)).thenReturn(false);
        
        // When & Then
        assertThatThrownBy(() -> bookService.updateBook(bookId, validBook))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Book with ID 999 does not exist");
    }
    
    @Test
    void deleteBook_WithValidId_ShouldReturnTrue() {
        // Given
        Long bookId = 1L;
        when(bookRepository.deleteById(bookId)).thenReturn(true);
        
        // When
        boolean result = bookService.deleteBook(bookId);
        
        // Then
        assertThat(result).isTrue();
        verify(bookRepository).deleteById(bookId);
    }
    
    @Test
    void deleteBook_WithNonExistentId_ShouldReturnFalse() {
        // Given
        Long bookId = 999L;
        when(bookRepository.deleteById(bookId)).thenReturn(false);
        
        // When
        boolean result = bookService.deleteBook(bookId);
        
        // Then
        assertThat(result).isFalse();
        verify(bookRepository).deleteById(bookId);
    }
}
