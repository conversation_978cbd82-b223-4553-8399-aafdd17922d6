package com.example.finpension.presentation.controller;

import com.example.finpension.presentation.dto.BookRequestDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;
import static org.hamcrest.Matchers.*;

@SpringBootTest
@AutoConfigureMockMvc
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.ANY)
@Transactional
class BookControllerIntegrationTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Autowired
    private ObjectMapper objectMapper;
    
    @Test
    void getAllBooks_ShouldReturnListOfBooks() throws Exception {
        mockMvc.perform(get("/api/books"))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$", isA(java.util.List.class)));
    }
    
    @Test
    void createBook_WithValidData_ShouldReturnCreatedBook() throws Exception {
        BookRequestDto requestDto = new BookRequestDto(
                "New Test Book",
                "Test Author",
                250,
                LocalDate.of(2023, 1, 1)
        );
        
        mockMvc.perform(post("/api/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isCreated())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.title", is("New Test Book")))
                .andExpect(jsonPath("$.authorName", is("Test Author")))
                .andExpect(jsonPath("$.numberOfPages", is(250)))
                .andExpect(jsonPath("$.id", notNullValue()));
    }
    
    @Test
    void createBook_WithInvalidData_ShouldReturnBadRequest() throws Exception {
        BookRequestDto requestDto = new BookRequestDto(
                "", // Empty title
                "Test Author",
                -10, // Negative pages
                LocalDate.of(2023, 1, 1)
        );
        
        mockMvc.perform(post("/api/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isBadRequest())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.validationErrors", notNullValue()));
    }
    
    @Test
    void getBookById_WithValidId_ShouldReturnBook() throws Exception {
        // First create a book
        BookRequestDto requestDto = new BookRequestDto(
                "Test Book for Get",
                "Test Author",
                200,
                LocalDate.of(2023, 1, 1)
        );
        
        String response = mockMvc.perform(post("/api/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        // Extract the ID from the response
        Long bookId = objectMapper.readTree(response).get("id").asLong();
        
        // Then get the book by ID
        mockMvc.perform(get("/api/books/{id}", bookId))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(bookId.intValue())))
                .andExpect(jsonPath("$.title", is("Test Book for Get")));
    }
    
    @Test
    void getBookById_WithInvalidId_ShouldReturnNotFound() throws Exception {
        mockMvc.perform(get("/api/books/{id}", 999999L))
                .andExpect(status().isNotFound());
    }
    
    @Test
    void updateBook_WithValidData_ShouldReturnUpdatedBook() throws Exception {
        // First create a book
        BookRequestDto createDto = new BookRequestDto(
                "Original Title",
                "Original Author",
                200,
                LocalDate.of(2023, 1, 1)
        );
        
        String createResponse = mockMvc.perform(post("/api/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(createDto)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Long bookId = objectMapper.readTree(createResponse).get("id").asLong();
        
        // Then update the book
        BookRequestDto updateDto = new BookRequestDto(
                "Updated Title",
                "Updated Author",
                300,
                LocalDate.of(2023, 2, 1)
        );
        
        mockMvc.perform(put("/api/books/{id}", bookId)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateDto)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.id", is(bookId.intValue())))
                .andExpect(jsonPath("$.title", is("Updated Title")))
                .andExpect(jsonPath("$.authorName", is("Updated Author")))
                .andExpect(jsonPath("$.numberOfPages", is(300)));
    }
    
    @Test
    void deleteBook_WithValidId_ShouldReturnNoContent() throws Exception {
        // First create a book
        BookRequestDto requestDto = new BookRequestDto(
                "Book to Delete",
                "Test Author",
                200,
                LocalDate.of(2023, 1, 1)
        );
        
        String response = mockMvc.perform(post("/api/books")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(requestDto)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString();
        
        Long bookId = objectMapper.readTree(response).get("id").asLong();
        
        // Then delete the book
        mockMvc.perform(delete("/api/books/{id}", bookId))
                .andExpect(status().isNoContent());
        
        // Verify the book is deleted
        mockMvc.perform(get("/api/books/{id}", bookId))
                .andExpect(status().isNotFound());
    }
}
