package com.example.finpension.infrastructure.persistence.adapter;

import com.example.finpension.domain.model.Book;
import com.example.finpension.infrastructure.persistence.entity.BookEntity;
import com.example.finpension.infrastructure.persistence.mapper.BookMapper;
import com.example.finpension.infrastructure.persistence.repository.BookJpaRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BookRepositoryAdapterTest {
    
    @Mock
    private BookJpaRepository bookJpaRepository;
    
    @Mock
    private BookMapper bookMapper;
    
    private BookRepositoryAdapter bookRepositoryAdapter;
    
    private Book domainBook;
    private BookEntity bookEntity;
    
    @BeforeEach
    void setUp() {
        bookRepositoryAdapter = new BookRepositoryAdapter(bookJpaRepository, bookMapper);
        
        domainBook = new Book(1L, "Test Title", "Test Author", 200, LocalDate.of(2023, 1, 1));
        bookEntity = new BookEntity(1L, "Test Title", "Test Author", 200, LocalDate.of(2023, 1, 1));
    }
    
    @Test
    void save_ShouldMapAndSaveBook() {
        // Given
        when(bookMapper.toEntity(domainBook)).thenReturn(bookEntity);
        when(bookJpaRepository.save(bookEntity)).thenReturn(bookEntity);
        when(bookMapper.toDomain(bookEntity)).thenReturn(domainBook);
        
        // When
        Book result = bookRepositoryAdapter.save(domainBook);
        
        // Then
        assertThat(result).isEqualTo(domainBook);
        verify(bookMapper).toEntity(domainBook);
        verify(bookJpaRepository).save(bookEntity);
        verify(bookMapper).toDomain(bookEntity);
    }
    
    @Test
    void findById_WhenBookExists_ShouldReturnBook() {
        // Given
        Long bookId = 1L;
        when(bookJpaRepository.findById(bookId)).thenReturn(Optional.of(bookEntity));
        when(bookMapper.toDomain(bookEntity)).thenReturn(domainBook);
        
        // When
        Optional<Book> result = bookRepositoryAdapter.findById(bookId);
        
        // Then
        assertThat(result).isPresent();
        assertThat(result.get()).isEqualTo(domainBook);
        verify(bookJpaRepository).findById(bookId);
        verify(bookMapper).toDomain(bookEntity);
    }
    
    @Test
    void findById_WhenBookDoesNotExist_ShouldReturnEmpty() {
        // Given
        Long bookId = 999L;
        when(bookJpaRepository.findById(bookId)).thenReturn(Optional.empty());
        
        // When
        Optional<Book> result = bookRepositoryAdapter.findById(bookId);
        
        // Then
        assertThat(result).isEmpty();
        verify(bookJpaRepository).findById(bookId);
        verify(bookMapper, never()).toDomain(any());
    }
    
    @Test
    void findAll_ShouldReturnAllBooks() {
        // Given
        List<BookEntity> entities = Arrays.asList(bookEntity);
        List<Book> expectedBooks = Arrays.asList(domainBook);
        
        when(bookJpaRepository.findAll()).thenReturn(entities);
        when(bookMapper.toDomain(bookEntity)).thenReturn(domainBook);
        
        // When
        List<Book> result = bookRepositoryAdapter.findAll();
        
        // Then
        assertThat(result).hasSize(1);
        assertThat(result).isEqualTo(expectedBooks);
        verify(bookJpaRepository).findAll();
        verify(bookMapper).toDomain(bookEntity);
    }
    
    @Test
    void findByAuthorName_ShouldReturnBooksByAuthor() {
        // Given
        String authorName = "Test Author";
        List<BookEntity> entities = Arrays.asList(bookEntity);
        List<Book> expectedBooks = Arrays.asList(domainBook);
        
        when(bookJpaRepository.findByAuthorName(authorName)).thenReturn(entities);
        when(bookMapper.toDomain(bookEntity)).thenReturn(domainBook);
        
        // When
        List<Book> result = bookRepositoryAdapter.findByAuthorName(authorName);
        
        // Then
        assertThat(result).hasSize(1);
        assertThat(result).isEqualTo(expectedBooks);
        verify(bookJpaRepository).findByAuthorName(authorName);
        verify(bookMapper).toDomain(bookEntity);
    }
    
    @Test
    void deleteById_WhenBookExists_ShouldReturnTrue() {
        // Given
        Long bookId = 1L;
        when(bookJpaRepository.existsById(bookId)).thenReturn(true);
        
        // When
        boolean result = bookRepositoryAdapter.deleteById(bookId);
        
        // Then
        assertThat(result).isTrue();
        verify(bookJpaRepository).existsById(bookId);
        verify(bookJpaRepository).deleteById(bookId);
    }
    
    @Test
    void deleteById_WhenBookDoesNotExist_ShouldReturnFalse() {
        // Given
        Long bookId = 999L;
        when(bookJpaRepository.existsById(bookId)).thenReturn(false);
        
        // When
        boolean result = bookRepositoryAdapter.deleteById(bookId);
        
        // Then
        assertThat(result).isFalse();
        verify(bookJpaRepository).existsById(bookId);
        verify(bookJpaRepository, never()).deleteById(bookId);
    }
    
    @Test
    void existsById_WhenBookExists_ShouldReturnTrue() {
        // Given
        Long bookId = 1L;
        when(bookJpaRepository.existsById(bookId)).thenReturn(true);
        
        // When
        boolean result = bookRepositoryAdapter.existsById(bookId);
        
        // Then
        assertThat(result).isTrue();
        verify(bookJpaRepository).existsById(bookId);
    }
}
